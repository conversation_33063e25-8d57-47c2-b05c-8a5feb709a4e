// Theme Switcher for Vector-Fire
// Handles switching between standard and retro mode

// Function to toggle between themes
function toggleTheme() {
    const body = document.body;

    // Toggle retro mode class
    body.classList.toggle('retro-mode');

    // Update button text
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.textContent = body.classList.contains('retro-mode')
            ? 'Standard Mode'
            : 'Retro Mode';
    }

    // Save preference to localStorage
    localStorage.setItem('vectorFireTheme', body.classList.contains('retro-mode') ? 'retro' : 'standard');

    // Play a retro toggle sound
    playToggleSound();
}

// Function to play a retro toggle sound
function playToggleSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.type = 'square';
        oscillator.frequency.setValueAtTime(document.body.classList.contains('retro-mode') ? 440 : 880, audioContext.currentTime);

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.3);
    } catch (e) {
        console.log('Audio not supported or enabled');
    }
}

// Function to create the theme toggle button
function createThemeToggle() {
    const themeToggle = document.createElement('button');
    themeToggle.id = 'theme-toggle';
    themeToggle.className = 'theme-toggle-button';
    themeToggle.textContent = document.body.classList.contains('retro-mode') ? 'Standard Mode' : 'Retro Mode';
    themeToggle.addEventListener('click', toggleTheme);

    // Find the appropriate container to append the button to
    // For observer.html
    let container = document.querySelector('#controls-container');

    // For mortar.html
    if (!container) {
        container = document.querySelector('#mortar-controls-container');
    }

    if (container) {
        // Create a dedicated section for the theme toggle
        const themeSection = document.createElement('section');
        themeSection.id = 'theme-settings';
        themeSection.className = 'control-panel';

        // Create a header for the section
        const header = document.createElement('h2');
        header.textContent = 'Display Settings';

        // Add elements to the DOM
        themeSection.appendChild(header);
        themeSection.appendChild(themeToggle);
        container.appendChild(themeSection);
    } else {
        // Fallback to body if containers aren't found
        document.body.appendChild(themeToggle);
    }
}

// Function to initialize theme based on saved preference
function initTheme() {
    const savedTheme = localStorage.getItem('vectorFireTheme');
    if (savedTheme === 'retro') {
        document.body.classList.add('retro-mode');
    }

    // Create the theme toggle button
    createThemeToggle();
}

// Initialize theme when DOM is loaded
document.addEventListener('DOMContentLoaded', initTheme);
