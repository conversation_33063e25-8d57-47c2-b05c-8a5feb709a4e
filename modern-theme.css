/* Modern Theme for Vector-Fire
   A professional, contemporary design for military applications */

:root {
  /* Enhanced modern color palette - more tactical feel */
  --modern-bg: #f4f6f9;
  --modern-panel-bg: #ffffff;
  --modern-panel-accent: #f8fafc;
  --modern-bg-pattern: rgba(29, 53, 87, 0.03);
  --modern-text: #1a2530;
  --modern-text-light: #64748b;
  --modern-text-accent: #334155;
  --modern-primary: #2563eb;
  --modern-primary-dark: #1d4ed8;
  --modern-primary-light: #3b82f6;
  --modern-secondary: #059669;
  --modern-secondary-dark: #047857;
  --modern-secondary-light: #10b981;
  --modern-danger: #dc2626;
  --modern-danger-dark: #b91c1c;
  --modern-danger-light: #ef4444;
  --modern-warning: #d97706;
  --modern-warning-light: #f59e0b;
  --modern-friendly: #2563eb;
  --modern-enemy: #dc2626;
  --modern-border: #e2e8f0;
  --modern-border-accent: #cbd5e1;
  --modern-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --modern-shadow-hover: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  --modern-shadow-focus: 0 0 0 3px rgba(37, 99, 235, 0.1);

  /* Typography */
  --modern-font-main: 'Inter', 'Roboto', 'Segoe UI', sans-serif;
  --modern-font-heading: 'Inter', 'Roboto Condensed', sans-serif;
  --modern-font-mono: 'JetBrains Mono', 'Roboto Mono', monospace;
}

/* Base styles */
body:not(.retro-mode) {
  background-color: var(--modern-bg);
  background-image:
    linear-gradient(var(--modern-bg-pattern) 1px, transparent 1px),
    linear-gradient(90deg, var(--modern-bg-pattern) 1px, transparent 1px);
  background-size: 20px 20px;
  color: var(--modern-text);
  font-family: var(--modern-font-main);
  line-height: 1.6;
  position: relative;
}

/* Typography */
body:not(.retro-mode) h1,
body:not(.retro-mode) h2,
body:not(.retro-mode) h3,
body:not(.retro-mode) h4 {
  font-family: var(--modern-font-heading);
  font-weight: 500;
  color: var(--modern-text);
  margin-bottom: 16px;
}

body:not(.retro-mode) h1 {
  font-size: 2.2rem;
  letter-spacing: -0.5px;
  border-bottom: 2px solid var(--modern-primary);
  padding-bottom: 8px;
  display: inline-block;
}

body:not(.retro-mode) h2 {
  font-size: 1.5rem;
  letter-spacing: -0.3px;
}

body:not(.retro-mode) h3 {
  font-size: 1.2rem;
  letter-spacing: -0.2px;
}

body:not(.retro-mode) p {
  margin-bottom: 16px;
}

/* Container */
body:not(.retro-mode) .container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(to bottom, rgba(255,255,255,0.7) 0%, rgba(255,255,255,0.9) 100%);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* Enhanced panels with tactical styling */
body:not(.retro-mode) .control-panel,
body:not(.retro-mode) #controls-container,
body:not(.retro-mode) #mortar-controls-container,
body:not(.retro-mode) .status-bar {
  background: linear-gradient(145deg, var(--modern-panel-bg), var(--modern-panel-accent));
  border-radius: 8px;
  box-shadow: var(--modern-shadow);
  border: 1px solid var(--modern-border);
  border-left: 3px solid var(--modern-primary);
  transition: all 0.2s ease;
  position: relative;
}

/* Tactical corner accents for control panels only */
body:not(.retro-mode) .control-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 16px;
  height: 16px;
  border-top: 2px solid var(--modern-primary);
  border-left: 2px solid var(--modern-primary);
  opacity: 0.4;
}

body:not(.retro-mode) .control-panel::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  border-bottom: 2px solid var(--modern-primary);
  border-right: 2px solid var(--modern-primary);
  opacity: 0.4;
}

/* Enhanced status bar with tactical styling */
body:not(.retro-mode) .status-bar {
  border-left: 4px solid var(--modern-primary);
  background: linear-gradient(90deg, var(--modern-panel-bg), var(--modern-panel-accent));
  border-top: 1px solid var(--modern-border-accent);
  border-bottom: 1px solid var(--modern-border-accent);
}

/* Status indicator enhancements */
body:not(.retro-mode) .status-indicator {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid var(--modern-border);
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

body:not(.retro-mode) .status-indicator:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: var(--modern-shadow-hover);
  transform: translateY(-1px);
}

body:not(.retro-mode) .status-indicator {
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid var(--modern-border);
  color: var(--modern-text);
}

body:not(.retro-mode) .status-indicator:hover {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: var(--modern-shadow-hover);
}

body:not(.retro-mode) .status-text {
  color: var(--modern-text);
}

/* Status icon colors for modern mode */
body:not(.retro-mode) #connection-status.connected .status-icon {
  color: var(--modern-secondary);
}

body:not(.retro-mode) #connection-status.disconnected .status-icon {
  color: var(--modern-danger);
}

body:not(.retro-mode) #connection-status.connecting .status-icon {
  color: var(--modern-warning);
}

body:not(.retro-mode) #auto-save-status.saved .status-icon,
body:not(.retro-mode) #mortar-ready-status.ready .status-icon {
  color: var(--modern-secondary);
}

body:not(.retro-mode) #auto-save-status.error .status-icon {
  color: var(--modern-danger);
}

body:not(.retro-mode) #mortar-ready-status.not-ready .status-icon {
  color: var(--modern-warning);
}

body:not(.retro-mode) .control-panel:hover {
  box-shadow: var(--modern-shadow-hover);
}

body:not(.retro-mode) section {
  padding: 20px;
  margin-bottom: 20px;
}

/* Collapsible sections */
body:not(.retro-mode) .collapsible {
  background-color: var(--modern-panel-bg);
  color: var(--modern-text);
  cursor: pointer;
  padding: 16px 20px;
  width: 100%;
  border: none;
  text-align: left;
  outline: none;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 8px;
  box-shadow: var(--modern-shadow);
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid var(--modern-border);
  margin-bottom: 16px;
}

body:not(.retro-mode) .collapsible:hover {
  background-color: #f8f9fa;
  box-shadow: var(--modern-shadow-hover);
}

body:not(.retro-mode) .collapsible:after {
  content: '\002B';
  color: var(--modern-primary);
  font-weight: bold;
  float: right;
  margin-left: 5px;
  transition: transform 0.3s ease;
}

body:not(.retro-mode) .active:after {
  content: "\2212";
  color: var(--modern-primary-dark);
}

body:not(.retro-mode) .content {
  padding: 0 20px;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background-color: var(--modern-panel-bg);
  border-radius: 0 0 8px 8px;
  border: 1px solid var(--modern-border);
  border-top: none;
  margin-top: -16px;
  margin-bottom: 20px;
}

body:not(.retro-mode) .active + .content {
  padding: 20px;
  border-top: none;
}

/* Enhanced form elements */
body:not(.retro-mode) input,
body:not(.retro-mode) select,
body:not(.retro-mode) textarea {
  width: 100%;
  padding: 12px 16px;
  margin-bottom: 16px;
  border: 2px solid var(--modern-border);
  border-radius: 6px;
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  color: var(--modern-text);
  font-family: var(--modern-font-main);
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

body:not(.retro-mode) input:focus,
body:not(.retro-mode) select:focus,
body:not(.retro-mode) textarea:focus {
  outline: none;
  border-color: var(--modern-primary);
  box-shadow: var(--modern-shadow-focus), inset 0 1px 3px rgba(0, 0, 0, 0.05);
  background: linear-gradient(145deg, #ffffff, #f0f9ff);
  transform: translateY(-1px);
}

body:not(.retro-mode) label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--modern-text);
}

/* Enhanced tactical buttons */
body:not(.retro-mode) button {
  background: linear-gradient(135deg, var(--modern-primary), var(--modern-primary-dark));
  color: white;
  border: 1px solid var(--modern-primary-dark);
  padding: 12px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-family: var(--modern-font-main);
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.2s ease;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--modern-shadow);
  position: relative;
  overflow: hidden;
}

/* Tactical button shine effect */
body:not(.retro-mode) button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

body:not(.retro-mode) button:hover::before {
  left: 100%;
}

body:not(.retro-mode) button:hover:not(:disabled) {
  background-color: var(--modern-primary-dark);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

body:not(.retro-mode) button:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

body:not(.retro-mode) button:disabled {
  background-color: #cbd5e0;
  color: #718096;
  cursor: not-allowed;
  box-shadow: none;
}

/* Action buttons */
body:not(.retro-mode) #add-target-button,
body:not(.retro-mode) #calculate-solution-button {
  background-color: var(--modern-secondary);
}

body:not(.retro-mode) #add-target-button:hover,
body:not(.retro-mode) #calculate-solution-button:hover {
  background-color: var(--modern-secondary-dark);
}

body:not(.retro-mode) #clear-targets-button,
body:not(.retro-mode) #clear-targets-button-mortar,
body:not(.retro-mode) .remove-button {
  background-color: var(--modern-danger);
}

body:not(.retro-mode) #clear-targets-button:hover,
body:not(.retro-mode) #clear-targets-button-mortar:hover,
body:not(.retro-mode) .remove-button:hover {
  background-color: var(--modern-danger-dark);
}

/* Button groups */
body:not(.retro-mode) .button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
}

/* Lists */
body:not(.retro-mode) #targets-list,
body:not(.retro-mode) #received-targets-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--modern-border);
  border-radius: 4px;
}

body:not(.retro-mode) #targets-list li,
body:not(.retro-mode) #received-targets-list li {
  padding: 12px 16px;
  border-bottom: 1px solid var(--modern-border);
  background-color: #fff;
  transition: all 0.2s ease;
  cursor: pointer;
}

body:not(.retro-mode) #targets-list li:last-child,
body:not(.retro-mode) #received-targets-list li:last-child {
  border-bottom: none;
}

body:not(.retro-mode) #targets-list li:hover,
body:not(.retro-mode) #received-targets-list li:hover {
  background-color: #f8fafc;
}

body:not(.retro-mode) #targets-list li.selected,
body:not(.retro-mode) #received-targets-list li.selected {
  background-color: rgba(52, 152, 219, 0.1);
  border-left: 3px solid var(--modern-primary);
}

/* Map container */
body:not(.retro-mode) #map-container,
body:not(.retro-mode) #mortar-map-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--modern-shadow);
  border: 1px solid var(--modern-border);
  transition: box-shadow 0.3s ease;
}

body:not(.retro-mode) #map-container:hover,
body:not(.retro-mode) #mortar-map-container:hover {
  box-shadow: var(--modern-shadow-hover);
}

body:not(.retro-mode) #map,
body:not(.retro-mode) #mortar-map {
  border-radius: 8px;
}

/* Map legend */
body:not(.retro-mode) #map-legend {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: var(--modern-shadow);
  border: 1px solid var(--modern-border);
  padding: 12px;
  transition: all 0.3s ease;
}

body:not(.retro-mode) #map-legend h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 1rem;
  color: var(--modern-text);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

body:not(.retro-mode) #map-legend.collapsed {
  background-color: rgba(255, 255, 255, 0.8);
}

body:not(.retro-mode) #map-legend.collapsed:hover {
  background-color: rgba(255, 255, 255, 0.95);
}

/* Force type indicators */
body:not(.retro-mode) .legend-friendly {
  background-color: var(--modern-friendly);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

body:not(.retro-mode) .legend-enemy {
  background-color: var(--modern-enemy);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Target markers */
body:not(.retro-mode) .target-marker.target-friendly div {
  background-color: var(--modern-friendly) !important;
  border-color: rgba(0, 0, 0, 0.2) !important;
}

body:not(.retro-mode) .target-marker.target-enemy div {
  background-color: var(--modern-enemy) !important;
  border-color: rgba(0, 0, 0, 0.2) !important;
}

/* Glow animations */
@keyframes modern-friendly-glow {
  0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
  50% { box-shadow: 0 0 8px 4px rgba(52, 152, 219, 0.4); }
  100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
}

@keyframes modern-enemy-glow {
  0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
  50% { box-shadow: 0 0 8px 4px rgba(231, 76, 60, 0.4); }
  100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
}

body:not(.retro-mode) .target-marker.target-friendly div {
  animation: modern-friendly-glow 2s infinite;
}

body:not(.retro-mode) .target-marker.target-enemy div {
  animation: modern-enemy-glow 2s infinite;
}

/* Scrollbar styling */
body:not(.retro-mode)::-webkit-scrollbar {
  width: 10px;
}

body:not(.retro-mode)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

body:not(.retro-mode)::-webkit-scrollbar-thumb {
  background: #c5d0db;
  border-radius: 10px;
}

body:not(.retro-mode)::-webkit-scrollbar-thumb:hover {
  background: #a9b9c9;
}

/* Solution display */
body:not(.retro-mode) #solution-display {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
  border: 1px solid var(--modern-border);
  transition: all 0.3s ease;
}

body:not(.retro-mode) #solution-display.calculated {
  background-color: rgba(46, 204, 113, 0.1);
  border-color: var(--modern-secondary);
}

body:not(.retro-mode) .calculation-confirmation {
  color: var(--modern-secondary-dark);
  font-weight: 600;
  text-align: center;
  margin-top: 12px;
  padding: 8px;
  background-color: rgba(46, 204, 113, 0.1);
  border-radius: 4px;
}

/* Theme toggle button */
body:not(.retro-mode) .theme-toggle-button {
  background-color: #34495e;
  color: white;
  border: none;
  font-weight: 500;
  letter-spacing: 0.5px;
}

body:not(.retro-mode) .theme-toggle-button:hover {
  background-color: #2c3e50;
}

/* Leaflet controls */
body:not(.retro-mode) .leaflet-control-zoom a {
  background-color: white;
  color: var(--modern-text);
  border-color: var(--modern-border);
  box-shadow: var(--modern-shadow);
  transition: all 0.2s ease;
}

body:not(.retro-mode) .leaflet-control-zoom a:hover {
  background-color: #f8fafc;
  color: var(--modern-primary);
}

/* Loading overlay */
body:not(.retro-mode) .loading-overlay {
  background-color: rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(3px);
  backdrop-filter: blur(3px);
  color: var(--modern-text);
  font-weight: 500;
}

/* Screen Navigation - Modern Style */
body:not(.retro-mode) .screen-navigation {
  background: linear-gradient(135deg, var(--modern-primary), var(--modern-secondary));
  border: none;
  box-shadow: var(--modern-shadow-hover);
}

body:not(.retro-mode) .screen-navigation h1 {
  color: white;
  font-family: var(--modern-font-heading);
  font-weight: 500;
  margin: 0;
}

body:not(.retro-mode) .nav-button {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-family: var(--modern-font-main);
  font-weight: 500;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

body:not(.retro-mode) .nav-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: var(--modern-shadow);
}

body:not(.retro-mode) .nav-button.active {
  background: rgba(255, 255, 255, 0.95);
  color: var(--modern-primary);
  border-color: white;
  font-weight: 600;
  box-shadow: var(--modern-shadow-hover);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  body:not(.retro-mode) h1 {
    font-size: 1.8rem;
  }

  body:not(.retro-mode) h2 {
    font-size: 1.3rem;
  }

  body:not(.retro-mode) section {
    padding: 15px;
  }

  body:not(.retro-mode) .button-group {
    flex-direction: column;
    gap: 8px;
  }
}

/* Add Google Fonts for the modern theme */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Condensed:wght@400;700&family=Roboto+Mono&display=swap');
