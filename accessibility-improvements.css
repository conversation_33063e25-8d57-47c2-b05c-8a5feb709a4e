/* Accessibility Improvements for Vector-Fire */

/* High contrast mode support */
@media (prefers-contrast: high) {
  body.retro-mode {
    --retro-text: #ffffff;
    --retro-highlight: #ffffff;
    --retro-enemy: #ffff00;
    --retro-bg: #000000;
  }
}

/* Focus indicators for keyboard navigation */
body.retro-mode button:focus,
body.retro-mode input:focus,
body.retro-mode select:focus {
  outline: 3px solid var(--retro-highlight);
  outline-offset: 2px;
  box-shadow: 0 0 0 1px var(--retro-bg), 0 0 0 4px var(--retro-highlight);
}

/* Skip links for screen readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--retro-highlight);
  color: var(--retro-bg);
  padding: 8px;
  text-decoration: none;
  z-index: 10000;
  font-weight: bold;
}

.skip-link:focus {
  top: 6px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Improved color contrast for status indicators */
body.retro-mode .status-indicator {
  border: 2px solid var(--retro-highlight);
}

/* Better focus management for interactive elements */
body.retro-mode .target-list-item:focus,
body.retro-mode .legend-item:focus {
  outline: 2px solid var(--retro-highlight);
  outline-offset: 1px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  body.retro-mode * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Large text support */
@media (min-resolution: 2dppx) {
  body.retro-mode {
    font-size: 16px;
  }
  
  body.retro-mode button {
    min-height: 44px;
    min-width: 44px;
  }
}
