/* Mobile Responsiveness Improvements for Vector-Fire */

/* Touch-friendly button sizes */
@media (max-width: 768px) {
  body.retro-mode button {
    min-height: 48px;
    min-width: 48px;
    padding: calc(var(--spacing-unit) * 2);
    font-size: 16px;
  }
  
  body.retro-mode .quick-action-button {
    width: 60px;
    height: 60px;
    font-size: 20px;
  }
  
  /* Larger form inputs for mobile */
  body.retro-mode input,
  body.retro-mode select {
    min-height: 44px;
    font-size: 16px;
    padding: calc(var(--spacing-unit) * 1.5);
  }
  
  /* Better spacing for mobile */
  body.retro-mode .control-panel {
    margin: calc(var(--spacing-unit) * 1);
    padding: calc(var(--spacing-unit) * 2);
  }
  
  /* Simplified animations for mobile performance */
  body.retro-mode::after {
    animation: none;
  }
  
  body.retro-mode::before {
    animation-duration: 0.3s;
  }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  body.retro-mode .map-controls-wrapper {
    flex-direction: column;
  }
  
  body.retro-mode #map-container,
  body.retro-mode #mortar-map-container {
    height: 400px;
    margin-bottom: calc(var(--spacing-unit) * 2);
  }
}

/* Landscape mobile optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  body.retro-mode .status-bar {
    padding: calc(var(--spacing-unit) * 1);
  }
  
  body.retro-mode .screen-navigation h1 {
    font-size: 1.5rem;
  }
  
  body.retro-mode .collapsible {
    padding: calc(var(--spacing-unit) * 1.5);
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  body.retro-mode {
    text-shadow: 0 0 4px rgba(0, 255, 0, 0.8);
  }
  
  body.retro-mode h1,
  body.retro-mode h2,
  body.retro-mode h3,
  body.retro-mode h4 {
    text-shadow: 0 0 6px rgba(127, 255, 0, 0.9);
  }
}
