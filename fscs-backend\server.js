// Import required modules
const express = require('express');
const cors = require('cors');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000; // Use port 3000 unless specified otherwise

// --- Middleware ---
// Enable CORS for all origins (adjust for production later if needed)
app.use(cors());
// Enable parsing of JSON request bodies
app.use(express.json());

// --- In-Memory Data Storage ---
// Simple variable to store the latest target list received from the observer
let currentTargets = [];

// --- API Routes ---

// Route to RECEIVE targets from the Observer
app.post('/api/sendTargets', (req, res) => {
    console.log('Received POST request on /api/sendTargets');
    try {
        const receivedTargets = req.body;

        // Basic validation: check if it's an array
        if (!Array.isArray(receivedTargets)) {
            console.error('Invalid data format received. Expected an array.');
            // Send a 400 Bad Request error
            return res.status(400).json({ message: 'Invalid data format. Expected an array.' });
        }

        // Store the received targets (overwriting previous list)
        currentTargets = receivedTargets;
        console.log(`Stored ${currentTargets.length} targets.`);
        // Log details of first target for verification (optional)
        // if (currentTargets.length > 0) {
        //     console.log('First target details:', JSON.stringify(currentTargets[0], null, 2));
        // }

        // Send a success response back to the Observer
        res.status(200).json({ message: 'Targets received successfully', count: currentTargets.length });

    } catch (error) {
        console.error('Error processing /api/sendTargets:', error);
        // Send a 500 Internal Server Error
        res.status(500).json({ message: 'Internal server error processing targets.' });
    }
});

// Route to PROVIDE targets to the Mortar Team window
app.get('/api/getTargets', (req, res) => {
    console.log(`Received GET request on /api/getTargets, sending ${currentTargets.length} targets.`);
    try {
        // Send the currently stored target list as JSON
        res.status(200).json(currentTargets);
    } catch (error) {
        console.error('Error processing /api/getTargets:', error);
        res.status(500).json({ message: 'Internal server error retrieving targets.' });
    }
});

// --- Start the Server ---
app.listen(PORT, () => {
    const currentTime = new Date().toLocaleTimeString('en-US', { timeZone: 'America/New_York' });
    console.log(`---\nFSC System Backend Server running on http://localhost:${PORT} at ${currentTime} EDT\n---`);
    console.log(`Observer should POST to http://localhost:${PORT}/api/sendTargets`);
    console.log(`Mortar Team should GET from http://localhost:${PORT}/api/getTargets`);
    console.log(`(Press Ctrl+C to stop the server)`);
});
