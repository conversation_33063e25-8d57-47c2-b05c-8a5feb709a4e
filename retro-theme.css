/* Retro Theme - Inspired by old-school monochrome computer terminals */

:root {
  /* Base colors - Enhanced contrast */
  --retro-bg: #000000;
  --retro-text: #00ff00; /* Brighter green for better contrast */
  --retro-dim: #228b22; /* Slightly brighter dimmer green */
  --retro-highlight: #7fff00; /* Chartreuse for better visibility */
  --retro-enemy: #ff6600; /* Brighter orange for enemy units */
  --retro-enemy-dim: #cc5500; /* Brighter dimmer orange */
  --retro-border: #00ff00;
  --retro-panel-bg: rgba(0, 30, 0, 0.9); /* More opaque for better contrast */
  --retro-input-bg: rgba(0, 50, 0, 0.8); /* More opaque for better readability */
  --retro-button-bg: #228b22;
  --retro-button-hover: #00ff00;
  --retro-shadow: 0 0 15px rgba(0, 255, 0, 0.6); /* Stronger glow */
  --retro-text-shadow: 0 0 8px rgba(0, 255, 0, 0.8); /* Enhanced text glow */

  /* Spacing remains the same */
  --spacing-unit: 8px;
}

/* Apply retro theme to body */
body.retro-mode {
  background-color: var(--retro-bg);
  color: var(--retro-text);
  font-family: 'Courier New', monospace;
  text-shadow: var(--retro-text-shadow);
}

/* Header styles */
body.retro-mode h1,
body.retro-mode h2,
body.retro-mode h3,
body.retro-mode h4 {
  color: var(--retro-highlight);
  text-shadow: 0 0 12px rgba(127, 255, 0, 0.9);
  font-weight: bold;
}

/* Ensure all text elements have proper contrast */
body.retro-mode p,
body.retro-mode li,
body.retro-mode span,
body.retro-mode label,
body.retro-mode .intro {
  color: var(--retro-text);
  text-shadow: var(--retro-text-shadow);
}

/* Panel backgrounds */
body.retro-mode .control-panel,
body.retro-mode #controls-container,
body.retro-mode #mortar-controls-container,
body.retro-mode .collapsible,
body.retro-mode .content,
body.retro-mode .status-bar {
  background-color: var(--retro-panel-bg);
  border: 1px solid var(--retro-dim);
  box-shadow: var(--retro-shadow);
  margin: calc(var(--spacing-unit) * 2);
  padding: calc(var(--spacing-unit) * 3);
}

/* Specific spacing adjustments for retro mode */
body.retro-mode .control-panel {
  margin-bottom: calc(var(--spacing-unit) * 3);
}

body.retro-mode .form-group {
  margin-bottom: calc(var(--spacing-unit) * 2.5);
}

body.retro-mode .button-group {
  margin-top: calc(var(--spacing-unit) * 3);
  gap: calc(var(--spacing-unit) * 2);
}

body.retro-mode .status-bar {
  margin: 0;
  margin-bottom: calc(var(--spacing-unit) * 2);
}

/* Status bar specific styling for retro mode */
body.retro-mode .status-bar {
  background-image: none;
  border-left: 4px solid var(--retro-highlight);
}

body.retro-mode .status-bar::before {
  background: none;
}

body.retro-mode .status-indicator {
  background-color: rgba(0, 40, 0, 0.7);
  border: 1px solid var(--retro-dim);
  color: var(--retro-text);
}

body.retro-mode .status-indicator:hover {
  background-color: rgba(0, 60, 0, 0.9);
  box-shadow: var(--retro-shadow);
}

body.retro-mode .status-text {
  color: var(--retro-text);
  text-shadow: var(--retro-text-shadow);
  font-weight: bold;
}

/* Status icon colors for retro mode */
body.retro-mode #connection-status.connected .status-icon {
  color: var(--retro-highlight);
  text-shadow: 0 0 5px var(--retro-highlight);
}

body.retro-mode #connection-status.disconnected .status-icon {
  color: var(--retro-enemy);
  text-shadow: 0 0 5px var(--retro-enemy);
}

body.retro-mode #connection-status.connecting .status-icon {
  color: var(--retro-text);
  text-shadow: 0 0 5px var(--retro-text);
}

body.retro-mode #auto-save-status.saved .status-icon,
body.retro-mode #mortar-ready-status.ready .status-icon {
  color: var(--retro-highlight);
  text-shadow: 0 0 5px var(--retro-highlight);
}

body.retro-mode #auto-save-status.error .status-icon {
  color: var(--retro-enemy);
  text-shadow: 0 0 5px var(--retro-enemy);
}

body.retro-mode #mortar-ready-status.not-ready .status-icon {
  color: var(--retro-enemy-dim);
  text-shadow: 0 0 5px var(--retro-enemy-dim);
}

/* Override any white backgrounds from modern theme */
body.retro-mode input,
body.retro-mode select,
body.retro-mode textarea,
body.retro-mode #targets-list li,
body.retro-mode #received-targets-list li,
body.retro-mode .leaflet-control-zoom a,
body.retro-mode .legend-content,
body.retro-mode .legend-item,
body.retro-mode .quick-action-button,
body.retro-mode .zoom-buttons button,
body.retro-mode .nav-button,
body.retro-mode .collapsible,
body.retro-mode .content {
  background-color: var(--retro-input-bg) !important;
  color: var(--retro-text) !important;
  border-color: var(--retro-dim) !important;
}

/* Ensure all containers have retro background */
body.retro-mode .container,
body.retro-mode .map-controls-wrapper,
body.retro-mode #map-container,
body.retro-mode #mortar-map-container,
body.retro-mode .send-feedback-area,
body.retro-mode .quick-action-toolbar {
  background: var(--retro-bg) !important;
  border-color: var(--retro-dim) !important;
}

/* Fix any remaining white elements - more specific targeting */
body.retro-mode .leaflet-popup-content,
body.retro-mode .leaflet-control-attribution {
  background-color: var(--retro-panel-bg) !important;
  color: var(--retro-text) !important;
}

body.retro-mode .leaflet-popup-content-wrapper,
body.retro-mode .leaflet-popup-tip {
  background-color: var(--retro-panel-bg) !important;
  color: var(--retro-text) !important;
}

body.retro-mode .leaflet-control-zoom a:hover {
  background-color: var(--retro-button-hover) !important;
  color: var(--retro-bg) !important;
}

/* Ensure container has proper retro background */
body.retro-mode .container {
  background: var(--retro-bg) !important;
  box-shadow: none !important;
}

/* Form elements */
body.retro-mode input,
body.retro-mode select,
body.retro-mode textarea {
  background-color: var(--retro-input-bg);
  color: var(--retro-highlight);
  border: 1px solid var(--retro-dim);
  font-family: 'Courier New', 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  font-weight: bold;
  text-shadow: var(--retro-text-shadow);
}

/* Ensure all text elements use monospace font in retro mode */
body.retro-mode,
body.retro-mode *:not(.leaflet-control):not(.leaflet-popup) {
  font-family: 'Courier New', 'Consolas', 'Monaco', monospace !important;
}

/* Button typography */
body.retro-mode button {
  font-family: 'Courier New', 'Consolas', 'Monaco', monospace;
  font-size: 13px;
  font-weight: bold;
  text-shadow: var(--retro-text-shadow);
}

body.retro-mode input:focus,
body.retro-mode select:focus,
body.retro-mode textarea:focus {
  border-color: var(--retro-highlight);
  outline: none;
  box-shadow: 0 0 5px var(--retro-highlight);
}

/* Buttons */
body.retro-mode button {
  background-color: var(--retro-button-bg);
  color: var(--retro-highlight);
  border: 2px solid var(--retro-dim);
  font-family: 'Courier New', 'Consolas', 'Monaco', monospace;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  text-shadow: var(--retro-text-shadow);
  border-radius: 0; /* Sharp corners for retro look */
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 2);
}

body.retro-mode button:hover:not(:disabled) {
  background-color: var(--retro-button-hover);
  color: var(--retro-bg);
  box-shadow: 0 0 10px var(--retro-highlight);
}

body.retro-mode button:disabled {
  background-color: rgba(26, 140, 26, 0.3);
  color: rgba(51, 255, 51, 0.5);
  cursor: not-allowed;
}

/* Target list */
body.retro-mode #targets-list li,
body.retro-mode #received-targets-list li {
  border-color: var(--retro-dim);
  background-color: rgba(0, 30, 0, 0.5);
}

body.retro-mode #targets-list li:hover,
body.retro-mode #received-targets-list li:hover {
  background-color: rgba(0, 50, 0, 0.7);
}

body.retro-mode #targets-list li.selected,
body.retro-mode #received-targets-list li.selected {
  background-color: rgba(0, 70, 0, 0.9);
  border-color: var(--retro-highlight);
}

/* --- Target Summary Dashboard - Retro Mode --- */
body.retro-mode #target-summary-section {
  margin-bottom: calc(var(--spacing-unit) * 3) !important;
}

body.retro-mode #target-summary-section h2 {
  color: var(--retro-highlight) !important;
  text-shadow: 0 0 10px rgba(51, 255, 51, 0.8) !important;
  font-family: 'Courier New', 'Consolas', 'Monaco', monospace !important;
  text-transform: uppercase !important;
  letter-spacing: 0.1em !important;
  border-bottom: 2px solid var(--retro-dim) !important;
  padding-bottom: calc(var(--spacing-unit) * 1) !important;
  margin-bottom: calc(var(--spacing-unit) * 2) !important;
}

body.retro-mode .summary-dashboard {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)) !important;
  gap: calc(var(--spacing-unit) * 1.5) !important;
  margin-bottom: calc(var(--spacing-unit) * 2) !important;
}

body.retro-mode .summary-card {
  background: linear-gradient(135deg, var(--retro-panel-bg), rgba(0, 30, 0, 0.8)) !important;
  border: 2px solid var(--retro-dim) !important;
  border-radius: 0 !important; /* Sharp corners for retro look */
  box-shadow: 0 0 10px rgba(51, 255, 51, 0.3),
              inset 0 1px 0 rgba(51, 255, 51, 0.2) !important;
  -webkit-backdrop-filter: none !important;
  backdrop-filter: none !important;
}

body.retro-mode .summary-card::before {
  background: linear-gradient(90deg, var(--retro-highlight), var(--retro-dim), var(--retro-highlight)) !important;
  height: 3px !important;
}

body.retro-mode .summary-card:hover {
  transform: translateY(-2px) !important;
  background: linear-gradient(135deg, rgba(0, 40, 0, 0.9), var(--retro-panel-bg)) !important;
  border-color: var(--retro-highlight) !important;
  box-shadow: 0 0 15px rgba(51, 255, 51, 0.5),
              inset 0 1px 0 rgba(51, 255, 51, 0.3) !important;
}

body.retro-mode .summary-card:hover::before {
  height: 4px !important;
  box-shadow: 0 0 8px var(--retro-highlight) !important;
}

body.retro-mode .summary-icon {
  background: linear-gradient(135deg, rgba(51, 255, 51, 0.2), rgba(0, 30, 0, 0.8)) !important;
  color: var(--retro-text) !important;
  border: 1px solid var(--retro-dim) !important;
  box-shadow: 0 0 8px rgba(51, 255, 51, 0.3),
              inset 0 1px 0 rgba(51, 255, 51, 0.2) !important;
  -webkit-backdrop-filter: none !important;
  backdrop-filter: none !important;
}

body.retro-mode .summary-icon.enemy {
  background: linear-gradient(135deg, rgba(255, 170, 0, 0.3), rgba(30, 15, 0, 0.8)) !important;
  color: var(--retro-enemy) !important;
  border-color: var(--retro-enemy-dim) !important;
  box-shadow: 0 0 8px rgba(255, 170, 0, 0.4),
              inset 0 1px 0 rgba(255, 170, 0, 0.2) !important;
}

body.retro-mode .summary-icon.friendly {
  background: linear-gradient(135deg, rgba(51, 255, 51, 0.3), rgba(0, 30, 0, 0.8)) !important;
  color: var(--retro-highlight) !important;
  border-color: var(--retro-dim) !important;
  box-shadow: 0 0 8px rgba(51, 255, 51, 0.4),
              inset 0 1px 0 rgba(51, 255, 51, 0.2) !important;
}

body.retro-mode .summary-icon.priority {
  background: linear-gradient(135deg, rgba(255, 255, 51, 0.3), rgba(30, 30, 0, 0.8)) !important;
  color: #ffff33 !important;
  border-color: rgba(255, 255, 51, 0.5) !important;
  box-shadow: 0 0 8px rgba(255, 255, 51, 0.4),
              inset 0 1px 0 rgba(255, 255, 51, 0.2) !important;
}

body.retro-mode .summary-number {
  color: var(--retro-text) !important;
  text-shadow: 0 0 8px rgba(51, 255, 51, 0.8) !important;
  font-family: 'Courier New', 'Consolas', 'Monaco', monospace !important;
  font-weight: bold !important;
}

body.retro-mode .summary-label {
  color: var(--retro-dim) !important;
  text-shadow: 0 0 4px rgba(51, 255, 51, 0.5) !important;
  font-family: 'Courier New', 'Consolas', 'Monaco', monospace !important;
  text-transform: uppercase !important;
  letter-spacing: 0.1em !important;
}

body.retro-mode .summary-card:hover .summary-number {
  color: var(--retro-highlight) !important;
  text-shadow: 0 0 12px rgba(51, 255, 51, 1) !important;
  transform: scale(1.05) !important;
}

body.retro-mode .summary-card:hover .summary-label {
  color: var(--retro-text) !important;
  text-shadow: 0 0 6px rgba(51, 255, 51, 0.7) !important;
}

/* Responsive design for retro mode Target Summary */
@media (max-width: 768px) {
  body.retro-mode .summary-card {
    padding: calc(var(--spacing-unit) * 1.5) !important;
  }

  body.retro-mode .summary-icon {
    font-size: 1.5rem !important;
    width: 35px !important;
    height: 35px !important;
  }

  body.retro-mode .summary-number {
    font-size: 1.5rem !important;
  }

  body.retro-mode .summary-label {
    font-size: 0.75rem !important;
  }
}

/* Retro-specific animations */
body.retro-mode .summary-card.updated .summary-number {
  animation: retro-number-update 0.6s ease-out !important;
}

@keyframes retro-number-update {
  0% {
    transform: scale(1.2);
    color: var(--retro-highlight);
    text-shadow: 0 0 15px rgba(51, 255, 51, 1);
  }
  100% {
    transform: scale(1);
    color: var(--retro-text);
    text-shadow: 0 0 8px rgba(51, 255, 51, 0.8);
  }
}

/* Map container */
body.retro-mode #map-container,
body.retro-mode #mortar-map-container {
  border: 1px solid var(--retro-dim);
  box-shadow: var(--retro-shadow);
}

/* Map legend */
body.retro-mode #map-legend {
  background-color: var(--retro-panel-bg);
  border: 1px solid var(--retro-dim);
}

body.retro-mode #map-legend.collapsed {
  background-color: rgba(0, 20, 0, 0.7);
}

body.retro-mode #map-legend.collapsed:hover {
  background-color: rgba(0, 30, 0, 0.9);
}

/* Force type indicators for the legend */
body.retro-mode .legend-friendly {
  background-color: var(--retro-highlight);
  border: 1px solid var(--retro-dim);
}

body.retro-mode .legend-enemy {
  background-color: var(--retro-enemy);
  border: 1px solid var(--retro-enemy-dim);
}

/* Target markers on map */
body.retro-mode .target-marker.target-friendly div {
  background-color: var(--retro-highlight) !important;
  border-color: var(--retro-dim) !important;
}

body.retro-mode .target-marker.target-enemy div {
  background-color: var(--retro-enemy) !important;
  border-color: var(--retro-enemy-dim) !important;
}

/* Mortar position marker */
body.retro-mode .mortar-position-marker div {
  background-color: var(--retro-highlight) !important;
  border-color: var(--retro-dim) !important;
}

/* Glow animations */
@keyframes retro-friendly-glow {
  0% { box-shadow: 0 0 0 0 rgba(51, 255, 51, 0.7); }
  50% { box-shadow: 0 0 8px 4px rgba(51, 255, 51, 0.5); }
  100% { box-shadow: 0 0 0 0 rgba(51, 255, 51, 0.7); }
}

@keyframes retro-enemy-glow {
  0% { box-shadow: 0 0 0 0 rgba(255, 170, 0, 0.7); }
  50% { box-shadow: 0 0 8px 4px rgba(255, 170, 0, 0.5); }
  100% { box-shadow: 0 0 0 0 rgba(255, 170, 0, 0.7); }
}

body.retro-mode .target-marker.target-friendly div {
  animation: retro-friendly-glow 2s infinite;
}

body.retro-mode .target-marker.target-enemy div {
  animation: retro-enemy-glow 2s infinite;
}

/* CRT effect - subtle scanlines */
body.retro-mode::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    rgba(0, 0, 0, 0) 50%,
    rgba(0, 0, 0, 0.1) 50%
  );
  background-size: 100% 3px;
  z-index: 9999;
  pointer-events: none;
  opacity: 0.2;
  animation: scanlines 0.1s linear infinite;
}

/* Scanline animation */
@keyframes scanlines {
  0% { transform: translateY(0); }
  100% { transform: translateY(3px); }
}

/* Screen flicker effect */
body.retro-mode::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--retro-bg);
  z-index: -1;
  animation: flicker 0.15s infinite linear alternate;
}

@keyframes flicker {
  0% { opacity: 1; }
  98% { opacity: 1; }
  99% { opacity: 0.98; }
  100% { opacity: 1; }
}

/* Retro cursor */
body.retro-mode * {
  cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" style="fill:none;stroke:%2333ff33;stroke-width:2px;"><rect width="8" height="16"/></svg>') 4 8, auto !important;
}

/* Theme settings section */
#theme-settings {
  margin-top: 20px;
}

/* Theme toggle button */
.theme-toggle-button {
  width: 100%;
  padding: 10px;
  margin-top: 10px;
  background-color: #444;
  color: white;
  border: 1px solid #555;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s;
  text-align: center;
}

body.retro-mode .theme-toggle-button {
  background-color: var(--retro-button-bg);
  color: var(--retro-highlight);
  border: 1px solid var(--retro-dim);
}

.theme-toggle-button:hover {
  background-color: #555;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

body.retro-mode .theme-toggle-button:hover {
  background-color: var(--retro-button-hover);
  color: var(--retro-bg);
  box-shadow: 0 0 10px var(--retro-highlight);
}

/* Leaflet map overrides for retro mode */
body.retro-mode .leaflet-container {
  background: #001400;
}

body.retro-mode .leaflet-tile {
  filter: grayscale(100%) brightness(30%) sepia(100%) hue-rotate(70deg) saturate(400%) brightness(50%);
}

body.retro-mode .leaflet-control-zoom a {
  background-color: var(--retro-panel-bg);
  color: var(--retro-text);
  border-color: var(--retro-dim);
}

body.retro-mode .leaflet-control-zoom a:hover {
  background-color: var(--retro-button-hover);
  color: var(--retro-bg);
}

/* Screen Navigation - Retro Style */
body.retro-mode .screen-navigation {
  background: linear-gradient(135deg, var(--retro-bg), var(--retro-panel-bg));
  border: 2px solid var(--retro-dim);
  box-shadow: var(--retro-shadow);
  color: var(--retro-text);
}

body.retro-mode .screen-navigation h1 {
  color: var(--retro-highlight);
  text-shadow: 0 0 8px rgba(95, 255, 95, 0.7);
}

body.retro-mode .nav-button {
  background: var(--retro-button-bg);
  color: var(--retro-text);
  border: 1px solid var(--retro-dim);
  text-shadow: 0 0 3px rgba(51, 255, 51, 0.5);
  backdrop-filter: none;
}

body.retro-mode .nav-button:hover:not(:disabled) {
  background: var(--retro-button-hover);
  color: var(--retro-bg);
  border-color: var(--retro-highlight);
  box-shadow: 0 0 10px rgba(51, 255, 51, 0.5);
  text-shadow: none;
}

body.retro-mode .nav-button.active {
  background: var(--retro-highlight);
  color: var(--retro-bg);
  border-color: var(--retro-highlight);
  text-shadow: none;
  box-shadow: 0 0 15px rgba(95, 255, 95, 0.8);
}

/* Retro-style scrollbar */
body.retro-mode::-webkit-scrollbar {
  width: 12px;
}

body.retro-mode::-webkit-scrollbar-track {
  background: var(--retro-bg);
}

body.retro-mode::-webkit-scrollbar-thumb {
  background-color: var(--retro-dim);
  border-radius: 0;
  border: 1px solid var(--retro-highlight);
}

body.retro-mode::-webkit-scrollbar-thumb:hover {
  background-color: var(--retro-highlight);
}

/* Blinking cursor effect for inputs */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

body.retro-mode input:focus::after {
  content: '|';
  margin-left: 2px;
  animation: blink 1s step-end infinite;
}
