/* Performance Optimizations for Vector-Fire */

/* Reduce animation complexity for better performance */
@media (prefers-reduced-motion: reduce) {
  body.retro-mode::after,
  body.retro-mode::before,
  .target-marker div,
  .retro-friendly-glow,
  .retro-enemy-glow {
    animation: none !important;
  }
}

/* Use transform instead of box-shadow for animations */
@keyframes retro-friendly-glow-optimized {
  0% { transform: scale(1); filter: drop-shadow(0 0 0 rgba(51, 255, 51, 0.7)); }
  50% { transform: scale(1.05); filter: drop-shadow(0 0 8px rgba(51, 255, 51, 0.5)); }
  100% { transform: scale(1); filter: drop-shadow(0 0 0 rgba(51, 255, 51, 0.7)); }
}

@keyframes retro-enemy-glow-optimized {
  0% { transform: scale(1); filter: drop-shadow(0 0 0 rgba(255, 170, 0, 0.7)); }
  50% { transform: scale(1.05); filter: drop-shadow(0 0 8px rgba(255, 170, 0, 0.5)); }
  100% { transform: scale(1); filter: drop-shadow(0 0 0 rgba(255, 170, 0, 0.7)); }
}

/* GPU acceleration for smooth animations */
body.retro-mode .target-marker div,
body.retro-mode button,
body.retro-mode .control-panel {
  will-change: transform;
  transform: translateZ(0);
}

/* Optimize scanlines for better performance */
@keyframes scanlines-optimized {
  0% { transform: translate3d(0, 0, 0); }
  100% { transform: translate3d(0, 3px, 0); }
}

/* Reduce flicker frequency for better performance */
@keyframes flicker-optimized {
  0% { opacity: 1; }
  95% { opacity: 1; }
  97% { opacity: 0.98; }
  100% { opacity: 1; }
}
